import * as React from "react";
import {FC, useState} from "react";
import {
    Box,
    IconButton,
    Typography,
    Collapse,
    makeStyles,
    Theme,
    Tooltip,
    Badge
} from "@eccosolutions/ecco-mui";
import {
    AttachFile as AttachFileIcon,
    ExpandMore as ExpandMoreIcon,
    ExpandLess as ExpandLessIcon
} from "@eccosolutions/ecco-mui-controls";
import {EvidenceFileUploadExample} from "./EvidenceFileUploadExample";
import {useEvidencePageContext} from "../EvidencePageRoot";

const useStyles = makeStyles((theme: Theme) => ({
    container: {
        marginTop: theme.spacing(2),
        marginBottom: theme.spacing(2)
    },
    header: {
        "display": "flex",
        "alignItems": "center",
        "gap": theme.spacing(1),
        "cursor": "pointer",
        "padding": theme.spacing(1),
        "borderRadius": theme.shape.borderRadius,
        "transition": "background-color 0.2s ease",
        "&:hover": {
            backgroundColor: theme.palette.action.hover
        }
    },
    headerIcon: {
        color: theme.palette.primary.main
    },
    headerText: {
        fontWeight: 500,
        color: theme.palette.text.primary
    },
    expandIcon: {
        marginLeft: "auto",
        transition: "transform 0.2s ease"
    },
    collapseContent: {
        paddingTop: theme.spacing(1)
    },
    badge: {
        "& .MuiBadge-badge": {
            backgroundColor: theme.palette.primary.main,
            color: theme.palette.primary.contrastText
        }
    }
}));

interface CollapsibleEvidenceAttachmentsProps {
    /** Initial expanded state */
    defaultExpanded?: boolean;
    /** Show file count badge */
    showFileCount?: boolean;
}

/**
 * Collapsible wrapper for evidence attachments that hides the upload interface
 * behind an expandable icon/header
 */
export const CollapsibleEvidenceAttachments: FC<CollapsibleEvidenceAttachmentsProps> = ({
    defaultExpanded = false,
    showFileCount = true
}) => {
    const classes = useStyles();
    const [expanded, setExpanded] = useState(defaultExpanded);
    const {init} = useEvidencePageContext();

    // For now, we'll use a placeholder for file count
    // In a real implementation, this would come from the evidence context or API
    const fileCount = 0; // TODO: Get actual file count from context or API

    const handleToggle = () => {
        setExpanded(!expanded);
    };

    const handleKeyDown = (event: React.KeyboardEvent) => {
        if (event.key === "Enter" || event.key === " ") {
            event.preventDefault();
            handleToggle();
        }
    };

    return (
        <Box className={classes.container}>
            <Box
                className={classes.header}
                onClick={handleToggle}
                onKeyDown={handleKeyDown}
                role="button"
                tabIndex={0}
                aria-expanded={expanded}
                aria-label={`${expanded ? "Collapse" : "Expand"} evidence attachments`}
            >
                <Tooltip title="Evidence attachments">
                    {showFileCount && fileCount > 0 ? (
                        <Badge badgeContent={fileCount} className={classes.badge}>
                            <AttachFileIcon className={classes.headerIcon} />
                        </Badge>
                    ) : (
                        <AttachFileIcon className={classes.headerIcon} />
                    )}
                </Tooltip>

                <Typography variant="body1" className={classes.headerText}>
                    Evidence Attachments
                    {showFileCount && fileCount > 0 && (
                        <Typography component="span" variant="body2" color="textSecondary">
                            {" "}
                            ({fileCount} file{fileCount !== 1 ? "s" : ""})
                        </Typography>
                    )}
                </Typography>

                <IconButton
                    className={classes.expandIcon}
                    size="small"
                    aria-label={expanded ? "Collapse" : "Expand"}
                >
                    {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
            </Box>

            <Collapse in={expanded} timeout="auto" unmountOnExit>
                <Box className={classes.collapseContent}>
                    <EvidenceFileUploadExample />
                </Box>
            </Collapse>
        </Box>
    );
};

/**
 * Minimal icon-only version for tight spaces
 */
export const CompactEvidenceAttachments: FC<{
    defaultExpanded?: boolean;
}> = ({defaultExpanded = false}) => {
    const classes = useStyles();
    const [expanded, setExpanded] = useState(defaultExpanded);

    const handleToggle = () => {
        setExpanded(!expanded);
    };

    return (
        <Box className={classes.container}>
            <Box display="flex" alignItems="center" gap={1}>
                <Tooltip title={`${expanded ? "Hide" : "Show"} evidence attachments`}>
                    <IconButton
                        onClick={handleToggle}
                        size="small"
                        aria-label={`${expanded ? "Hide" : "Show"} evidence attachments`}
                    >
                        <AttachFileIcon className={classes.headerIcon} />
                    </IconButton>
                </Tooltip>
            </Box>

            <Collapse in={expanded} timeout="auto" unmountOnExit>
                <Box className={classes.collapseContent}>
                    <EvidenceFileUploadExample />
                </Box>
            </Collapse>
        </Box>
    );
};
