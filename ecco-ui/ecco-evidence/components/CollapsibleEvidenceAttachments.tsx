import * as React from "react";
import {FC, useState} from "react";
import {
    Box,
    IconButton,
    Typography,
    Collapse,
    makeStyles,
    Theme,
    Tooltip,
    Badge
} from "@eccosolutions/ecco-mui";
import {
    AttachFileIcon
} from "@eccosolutions/ecco-mui-controls";
import {EvidenceFileUploadExample} from "./EvidenceFileUploadExample";
import {useEvidencePageContext} from "../EvidencePageRoot";

const useStyles = makeStyles((theme: Theme) => ({
    container: {
        position: "relative",
        display: "inline-block"
    },
    iconButton: {
        color: theme.palette.primary.main,
        "&:hover": {
            backgroundColor: theme.palette.action.hover
        }
    },
    collapseContent: {
        position: "absolute",
        top: "100%",
        right: 0,
        zIndex: theme.zIndex.modal - 1,
        minWidth: 400,
        maxWidth: 500,
        backgroundColor: theme.palette.background.paper,
        border: `1px solid ${theme.palette.divider}`,
        borderRadius: theme.shape.borderRadius,
        boxShadow: theme.shadows[8],
        padding: theme.spacing(2),
        marginTop: theme.spacing(1)
    },
    badge: {
        "& .MuiBadge-badge": {
            backgroundColor: theme.palette.primary.main,
            color: theme.palette.primary.contrastText,
            fontSize: "0.75rem",
            minWidth: 16,
            height: 16
        }
    }
}));

interface CollapsibleEvidenceAttachmentsProps {
    /** Initial expanded state */
    defaultExpanded?: boolean;
    /** Show file count badge */
    showFileCount?: boolean;
}

/**
 * Collapsible wrapper for evidence attachments that shows as just an icon
 * on the right side, expanding to show the upload interface when clicked
 */
export const CollapsibleEvidenceAttachments: FC<CollapsibleEvidenceAttachmentsProps> = ({
    defaultExpanded = false,
    showFileCount = true
}) => {
    const classes = useStyles();
    const [expanded, setExpanded] = useState(defaultExpanded);
    const {init} = useEvidencePageContext();

    // For now, we'll use a placeholder for file count
    // In a real implementation, this would come from the evidence context or API
    const fileCount = 0; // TODO: Get actual file count from context or API

    const handleToggle = () => {
        setExpanded(!expanded);
    };

    return (
        <Box className={classes.container}>
            <Tooltip title={`${expanded ? "Hide" : "Show"} evidence attachments`}>
                <IconButton
                    className={classes.iconButton}
                    onClick={handleToggle}
                    size="small"
                    aria-label={`${expanded ? "Hide" : "Show"} evidence attachments`}
                >
                    {showFileCount && fileCount > 0 ? (
                        <Badge
                            badgeContent={fileCount}
                            className={classes.badge}
                        >
                            <AttachFileIcon />
                        </Badge>
                    ) : (
                        <AttachFileIcon />
                    )}
                </IconButton>
            </Tooltip>

            <Collapse in={expanded} timeout="auto" unmountOnExit>
                <Box className={classes.collapseContent}>
                    <EvidenceFileUploadExample />
                </Box>
            </Collapse>
        </Box>
    );
};

/**
 * Minimal icon-only version for tight spaces
 */
export const CompactEvidenceAttachments: FC<{
    defaultExpanded?: boolean;
}> = ({defaultExpanded = false}) => {
    const classes = useStyles();
    const [expanded, setExpanded] = useState(defaultExpanded);

    const handleToggle = () => {
        setExpanded(!expanded);
    };

    return (
        <Box className={classes.container}>
            <Box display="flex" alignItems="center" gap={1}>
                <Tooltip title={`${expanded ? "Hide" : "Show"} evidence attachments`}>
                    <IconButton
                        onClick={handleToggle}
                        size="small"
                        aria-label={`${expanded ? "Hide" : "Show"} evidence attachments`}
                    >
                        <AttachFileIcon className={classes.iconButton} />
                    </IconButton>
                </Tooltip>
            </Box>

            <Collapse in={expanded} timeout="auto" unmountOnExit>
                <Box className={classes.collapseContent}>
                    <EvidenceFileUploadExample />
                </Box>
            </Collapse>
        </Box>
    );
};
