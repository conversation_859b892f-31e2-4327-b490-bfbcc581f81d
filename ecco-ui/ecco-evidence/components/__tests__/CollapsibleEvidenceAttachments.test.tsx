import React from "react";
import {render, screen, fireEvent} from "@testing-library/react";
import {CollapsibleEvidenceAttachments} from "../CollapsibleEvidenceAttachments";
import {TestServicesContextProvider} from "ecco-components/test-support/TestServicesContextProvider";
import {EvidencePageRoot} from "../../EvidencePageRoot";
import {testEvidencePagePlanData} from "../../test-support/mockEvidence";
import {EvidencePageType} from "ecco-dto";

// Mock the EvidenceFileUploadExample component since we're just testing the collapsible behavior
jest.mock("../EvidenceFileUploadExample", () => ({
    EvidenceFileUploadExample: () => <div data-testid="evidence-upload">Evidence Upload Component</div>
}));

const TestWrapper: React.FC<{children: React.ReactNode}> = ({children}) => (
    <TestServicesContextProvider>
        <EvidencePageRoot
            initData={{
                ...testEvidencePagePlanData(EvidencePageType.assessmentReduction),
                readOnly: false
            }}
        >
            {children}
        </EvidencePageRoot>
    </TestServicesContextProvider>
);

describe("CollapsibleEvidenceAttachments", () => {
    it("renders as collapsed by default", () => {
        render(
            <TestWrapper>
                <CollapsibleEvidenceAttachments />
            </TestWrapper>
        );

        // Should show the attachment icon
        expect(screen.getByLabelText(/show evidence attachments/i)).toBeInTheDocument();
        
        // Should not show the upload component initially
        expect(screen.queryByTestId("evidence-upload")).not.toBeInTheDocument();
    });

    it("expands when icon is clicked", () => {
        render(
            <TestWrapper>
                <CollapsibleEvidenceAttachments />
            </TestWrapper>
        );

        const attachmentIcon = screen.getByLabelText(/show evidence attachments/i);
        
        // Click to expand
        fireEvent.click(attachmentIcon);
        
        // Should now show the upload component
        expect(screen.getByTestId("evidence-upload")).toBeInTheDocument();
        
        // Icon label should change to "hide"
        expect(screen.getByLabelText(/hide evidence attachments/i)).toBeInTheDocument();
    });

    it("collapses when clicked again", () => {
        render(
            <TestWrapper>
                <CollapsibleEvidenceAttachments />
            </TestWrapper>
        );

        const attachmentIcon = screen.getByLabelText(/show evidence attachments/i);
        
        // Click to expand
        fireEvent.click(attachmentIcon);
        expect(screen.getByTestId("evidence-upload")).toBeInTheDocument();
        
        // Click to collapse
        const hideIcon = screen.getByLabelText(/hide evidence attachments/i);
        fireEvent.click(hideIcon);
        
        // Should hide the upload component
        expect(screen.queryByTestId("evidence-upload")).not.toBeInTheDocument();
    });

    it("can start expanded", () => {
        render(
            <TestWrapper>
                <CollapsibleEvidenceAttachments defaultExpanded={true} />
            </TestWrapper>
        );

        // Should show the upload component immediately
        expect(screen.getByTestId("evidence-upload")).toBeInTheDocument();
        
        // Should show "hide" label
        expect(screen.getByLabelText(/hide evidence attachments/i)).toBeInTheDocument();
    });
});
