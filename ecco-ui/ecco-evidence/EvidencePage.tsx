import * as React from "react";
import {FC, useCallback, useEffect, useState, useRef} from "react";
import {
    LoadingSpinner,
    useServiceRecipient,
    useServicesContext
} from "ecco-components";
import {link, showNotification, muiPagePlanTheme} from "ecco-components-core";
import {
    allowMixedPageTotalSwitch,
    allowMixedQuestionsAndSmartSteps,
    createEvidenceDefForQuestionnaireWithSmartSteps,
    createEvidenceDefForSmartStepsWithQuestionnaire,
    stateDifferent,
    EvidencePageData,
    EvidencePageInit,
    EvidencePageReducer,
    EvidencePageRootForCommandForm,
    EvidencePageState,
    useEvidencePageContext
} from "./EvidencePageRoot";
import {Uuid} from "@eccosolutions/ecco-crypto";
import {
    ConfigResolverDefault,
    HistoryLinkEvent,
    QuestionnaireAnswersSnapshotDto,
    ServiceType,
    SessionData,
    SupportSmartStepsSnapshotDto,
    HactControlEvent,
    EvidenceUpdateEvent
} from "ecco-dto";
import {EvidenceDef} from "./domain";
import {CommentEntry} from "./CommentEntry";
import {Button, Container, Grid, IconButton, ThemeProvider} from "@eccosolutions/ecco-mui";
import {getOutcomeSections, getOutcomeTabs} from "./smartsteps/Outcomes";
import {getQuestionGroupTabs} from "./questionanswers/QuestionAnswers";
import {CommentEntryState} from "./CommentEntryRoot";
import {PrintIcon} from "@eccosolutions/ecco-mui-controls";
import {applicationRootPath} from "application-properties";
import {EvidencePageFilter} from "./EvidencePageFilter";
import {CollapsibleEvidenceAttachments} from "./components/CollapsibleEvidenceAttachments";


/**
 * Get the main evidence content. This is called after providers, so we have access to post-calculated init data.
 */
function getEvidenceContent(
    init: EvidencePageInit,
    sessionData: SessionData,
    state: EvidencePageState
) {
    const displayName = init.initData.configResolver
        .getServiceType()
        .lookupTaskName(init.initData.taskName);

    // the form to show is based on logic in 'EvidenceDelegatingForm.loadAndAttach'
    // type has already been checked previously, in the loader
    const type = EvidenceDef.taskEvidenceType(sessionData, init.initData.taskName)!;

    const allowMixed = allowMixedQuestionsAndSmartSteps(
        sessionData,
        init.initData.evidenceDef.getTaskName()
    );
    if (EvidenceDef.isQuestionnaire(type)) {
        // find any questions with a smart step id, and use that as the indication to allowSmartStep
        return getQuestionGroupTabs(state.questionAnswers, sessionData, allowMixed);
    } else if (EvidenceDef.isChecklist(type)) {
    } else {
        const totalSwitch = allowMixedPageTotalSwitch(init.initData);
        if (totalSwitch) {
            return getQuestionGroupTabs(state.questionAnswers, sessionData, false);
        } else {
            return getOutcomeTabs(state.smartSteps, sessionData, allowMixed);
        }
    }

    return <div>-missing-content-</div>;
}

function getSrIdTaskDirectHref(srId: number, taskName: string, printable = false) {
    // applicationRootPath is not available in tests - see ServicesProjectsBuildingsSelector.test.tsx
    if (printable) {
        return new URL(
            `${applicationRootPath}nav/service-recipient/${srId}/task/${taskName}/printable`,
            location.href
        ).href;
    }
    return new URL(`${applicationRootPath}nav/r/main/sr2/${srId}/tasks/${taskName}`, location.href)
        .href;
}
const PrintableLink = (props: {enabled: boolean; srId: number; taskName: string}) => {
    const openPage = () =>
        window.open(getSrIdTaskDirectHref(props.srId, props.taskName, true), "_blank");
    return (
        <IconButton onClick={openPage} disabled={!props.enabled}>
            <PrintIcon />
        </IconButton>
    );
};
const EditButton = (props: {enabled: boolean; edit: () => void}) => {
    return (
        <Button disabled={!props.enabled} variant="outlined" onClick={props.edit}>
            edit
        </Button>
    );
};


/**
 * External entry point for the EvidencePagePlan - assumes wrapped in EvidencePageSetupForCommandForm or EvidencePageRoot directly
 */
export const EvidencePagePlanLayout: FC = () => {
    const {init, state, dispatch} = useEvidencePageContext();

    // TODO as memo
    const outcomes = init.initData.configResolver.getOutcomesFilteredForTask(
        init.initData.taskName
    );
    const Outcomes = getOutcomeSections(outcomes, false);
    //const layout = useMemo<JSX.Element>(() => getEvidenceContent(init, sessionData, state), []);
    // const layout = useCallback(() => {
    //     return getEvidenceContent(init, sessionData, state)
    // }, []);
    const setter = useCallback((s: Partial<CommentEntryState>) => {
        dispatch({
            type: "updateCommentEntry",
            data: s
        });
    }, []);

    // HISTORY BUTTON
    // need to click the history which is in QuestionnaireHistoryListItem: ControlWrapper would be good, but we don't have access to the component from ecco-ui
    const historyClick = () =>
        HistoryLinkEvent.bus.fire(
            new HistoryLinkEvent(
                init.initData.serviceRecipientId,
                init.initData.taskName,
                "EVIDENCE_SUPPORT"
            )
        );

    const taskName = init.initData.evidenceDef.getTaskName();
    const serviceType = init.initData.configResolver.getServiceType();
    const top = serviceType.taskDefinitionSettingHasFlag(taskName, "commentLocation", "top");

    const CommentEntrySection = state.pageEditing && (
        <>
            {!top && <div style={{padding: "25px"}}>&nbsp;</div>}
            <CommentEntry
                init={init.initCommentEntry}
                state={state.commentEntry}
                stateSetter={setter}
            />
            {top && <div style={{padding: "25px"}}>&nbsp;</div>}
        </>
    );
    const CommentEntryTop = top && CommentEntrySection;
    const CommentEntryBottom = !top && CommentEntrySection;

    return (
        <ThemeProvider theme={muiPagePlanTheme}>
            <Container maxWidth="lg" key={"plan-layout-container"}>
                <Grid container justify={"center"}>
                    {state.pageEditing && (
                        <>
                            <Grid item>
                                <PrintableLink
                                    /* perhaps should use initHolder and stateHolder */
                                    enabled={
                                        !stateDifferent(
                                            init.initSmartSteps.map(s => s.initState),
                                            state.smartSteps
                                        )
                                    }
                                    srId={init.initData.serviceRecipientId}
                                    taskName={init.initData.taskName}
                                />
                            </Grid>
                            <Grid item>{link("history", historyClick)}</Grid>
                        </>
                    )}
                    {/*<EditButton
                        enabled={!state.pageEditing}
                        edit={() => dispatch({type: "pageEdit"})}
                    />*/}
                </Grid>
                <br />
                {CommentEntryTop}
                <Grid container>
                    <Grid item xs={12} sm={10} md={11}>
                        {/* Main content area */}
                    </Grid>
                    <Grid item xs={12} sm={2} md={1} container justify="flex-end">
                        <EvidencePageFilter />
                        {state.pageEditing && (
                            <CollapsibleEvidenceAttachments defaultExpanded={false} showFileCount={true} />
                        )}
                    </Grid>
                </Grid>
                {Outcomes}
                {CommentEntryBottom}
                <div style={{padding: "50px"}}>&nbsp;</div>
            </Container>
        </ThemeProvider>
    );
};

/**
 * External entry point for the EvidencePage - assumes wrapped in EvidencePageSetupForCommandForm or EvidencePageRoot directly
 * This is the newer evidence components that isn't our EvidencePagePlanLayout
 */
export const EvidencePageLayout: FC = () => {
    const {sessionData, clientRepository} = useServicesContext();
    const {init, state, dispatch} = useEvidencePageContext();

    // TODO as memo
    const layout = getEvidenceContent(init, sessionData, state);
    //const layout = useMemo<JSX.Element>(() => getEvidenceContent(init, sessionData, state), []);
    // const layout = useCallback(() => {
    //     return getEvidenceContent(init, sessionData, state)
    // }, []);

    const setter = useCallback((s: Partial<CommentEntryState>) => {
        dispatch({
            type: "updateCommentEntry",
            data: s
        });
    }, []);

    // trigger HACT control - see StatusPanel
    const hactEl = useRef<HTMLDivElement>(null);
    useEffect(() => {
        const isReferral = true; // see HactNotificationControl.hactEnabled
        if (
            isReferral &&
            hactEl.current &&
            init.initData.sessionData.hasHactForService(init.initData.serviceId)
        ) {
            clientRepository
                .findOneClientByServiceRecipientId(init.initData.serviceRecipientId)
                .then(c => {
                    // need to load the HactNotificationControl: ControlWrapper would be good, but we don't have access to the component from ecco-ui
                    HactControlEvent.bus.fire(
                        new HactControlEvent(
                            init.initData.serviceRecipientId,
                            c.clientId!,
                            hactEl.current!
                        )
                    );
                });
        }
    }, [init?.initData?.serviceRecipientId, hactEl]);

    // HISTORY BUTTON
    // need to click the history which is in QuestionnaireHistoryListItem: ControlWrapper would be good, but we don't have access to the component from ecco-ui
    const historyClick = () =>
        HistoryLinkEvent.bus.fire(
            new HistoryLinkEvent(
                init.initData.serviceRecipientId,
                init.initData.taskName,
                "EVIDENCE_QUESTIONNAIRE"
            )
        );

    return (
        <>
            <Grid container justify={"center"}>
                <Grid item>{link("history", historyClick)}</Grid>
            </Grid>
            <Grid container justify={"center"}>
                <div ref={hactEl} />
            </Grid>
            <div style={{padding: "25px"}}>&nbsp;</div>
            <CommentEntry
                init={init.initCommentEntry}
                state={state.commentEntry}
                stateSetter={setter}
            />
            <div style={{padding: "25px"}}>&nbsp;</div>
            <Container maxWidth="lg">
                <Grid container>
                    <Grid item xs={12} sm={10} md={11}>
                        {layout}
                    </Grid>
                    <Grid item xs={12} sm={2} md={1} container justify="flex-end">
                        <EvidencePageFilter />
                    </Grid>
                </Grid>
            </Container>
        </>
    );
};

/**
 * External entry point for the wiring
 * Fully functional EvidencePage assuming a CommandForm
 */
export const EvidencePageSetupForCommandForm: FC<{
    initData: EvidencePageData;
    actions?: EvidencePageReducer | undefined;
}> = props => {
    return (
        <EvidencePageRootForCommandForm initData={props.initData} actions={props.actions}>
            {props.children}
        </EvidencePageRootForCommandForm>
    );
};

export const EvidencePageLoaderForCommandForm: FC<{
    srId: number;
    taskName: string;
    readOnly: boolean;
}> = props => {
    const {
        sessionData,
        supportSmartStepsSnapshotRepository,
        questionnaireSnapshotRepository,
        serviceRecipientRepository
    } = useServicesContext();
    const {serviceRecipient} = useServiceRecipient(props.srId);

    const [serviceType, setServiceType] = useState<ServiceType>();
    const [evidenceDef, setEvidenceDef] = useState<EvidenceDef>();
    const [allowMixed, setAllowMixed] = useState<boolean>();
    const [snapshotSupportDtos, setSnapshotSupportDtos] = useState<SupportSmartStepsSnapshotDto>();
    const [snapshotQuestionAnswerDtos, setSnapshotQuestionAnswerDtos] =
        useState<QuestionnaireAnswersSnapshotDto | null>();

    useEffect(() => {
        if (serviceRecipient) {
            const st = sessionData.getServiceTypeByServiceCategorisationId(
                serviceRecipient.serviceAllocationId
            );
            const ed = EvidenceDef.fromTaskName(sessionData, st, props.taskName);
            setServiceType(st);
            setEvidenceDef(ed);

            const type = EvidenceDef.taskEvidenceType(sessionData, ed.getTaskName())!;
            const allowMixedEvidence = allowMixedQuestionsAndSmartSteps(
                sessionData,
                ed.getTaskName()
            );
            setAllowMixed(allowMixedEvidence);

            const loadAnswers = EvidenceDef.isQuestionnaire(type) || allowMixedEvidence;
            const loadSupport = EvidenceDef.isSupport(type) || allowMixedEvidence;
            const supportGroup = allowMixedEvidence
                ? createEvidenceDefForQuestionnaireWithSmartSteps(
                      sessionData,
                      st
                  ).getEvidenceGroup()
                : ed.getEvidenceGroup();
            // we may be undefined if we are a question page with smart steps
            // where the taskName is not configured, but set above with "needsAssessmentReduction"
            const questionGroupMaybe =
                allowMixedEvidence &&
                createEvidenceDefForSmartStepsWithQuestionnaire(
                    sessionData,
                    ed.getTaskName(),
                    st
                )?.getEvidenceGroup();
            const questionGroup = questionGroupMaybe ? questionGroupMaybe : ed.getEvidenceGroup();

            if (loadAnswers) {
                const questionnaireAsBlank = st.taskDefinitionSettingHasFlag(
                    ed.getTaskName(),
                    "questionnaireAsBlank",
                    "y"
                );
                if (questionnaireAsBlank) {
                    setSnapshotQuestionAnswerDtos(null);
                } else {
                    questionnaireSnapshotRepository
                        .findLatestAnswersByServiceRecipientIdAndEvidenceGroupKey(
                            props.srId,
                            questionGroup
                        )
                        .then(snapshot => setSnapshotQuestionAnswerDtos(snapshot));
                }
            }
            if (loadSupport) {
                supportSmartStepsSnapshotRepository
                    .findSupportSmartStepsSnapshotByServiceRecipientIdAndEvidenceGroup(
                        props.srId,
                        supportGroup.name
                    )
                    .then(snapshot => setSnapshotSupportDtos(snapshot));
            }

            serviceRecipientRepository
                .findServiceRecipientDraftCommands(
                    props.srId,
                    props.taskName,
                    sessionData.getDto().userId
                )
                .then(cmds => {
                    cmds.forEach(cmd => {
                        if (cmd.evidenceTask) {
                            const def = EvidenceDef.fromTaskName(sessionData, st, cmd.evidenceTask);
                            // TODO have the 'comment' area pick up on the draft, and form evidence / questionnaire
                            EvidenceUpdateEvent.bus(def.getEvidenceGroup().name).fire(
                                new EvidenceUpdateEvent(cmd)
                            );
                        }
                    });
                    if (cmds.length > 0) {
                        showNotification("info", "draft restored");
                    }
                });
        }
    }, [serviceRecipient]);

    if (
        !sessionData ||
        !serviceRecipient ||
        !evidenceDef ||
        !serviceType ||
        (snapshotSupportDtos === undefined && snapshotQuestionAnswerDtos === undefined) ||
        (allowMixed &&
            snapshotSupportDtos === undefined &&
            snapshotQuestionAnswerDtos === undefined)
    ) {
        return <LoadingSpinner />;
    }

    const type = EvidenceDef.taskEvidenceType(sessionData, evidenceDef.getTaskName());
    if (!type) {
        throw new Error("type cannot be empty");
    }

    let commentTypes = serviceType.getCommentTypesById(sessionData, props.taskName).map(ld => {
        return {
            id: ld!.getId(),
            name: ld!.getDisplayName(),
            disabled: ld!.getDisabled()
        };
    });

    const svcCat = sessionData.getServiceCategorisation(serviceRecipient.serviceAllocationId);
    const data: EvidencePageData = {
        sessionData: sessionData,
        serviceId: svcCat.serviceId, // for hact
        taskName: props.taskName,
        evidenceDef: evidenceDef,
        configResolver: ConfigResolverDefault.fromServiceRecipient(
            sessionData,
            serviceRecipient.serviceAllocationId,
            serviceRecipient.serviceTypeId
        ),
        workUuid: Uuid.randomV4(),
        workUuid2: Uuid.randomV4(),
        serviceRecipientId: props.srId,
        readOnly: props.readOnly,
        comment: undefined,
        workDate: undefined,
        minsSpent: undefined,
        commentTypeId: undefined,
        commentTypes: commentTypes,
        clientStatusId: undefined,
        meetingStatusId: undefined,
        supportActions: snapshotSupportDtos?.latestActions || [],
        questionAnswers: snapshotQuestionAnswerDtos?.answers || []
    };
    return (
        <EvidencePageSetupForCommandForm initData={data}>
            {props.children}
        </EvidencePageSetupForCommandForm>
    );
};
